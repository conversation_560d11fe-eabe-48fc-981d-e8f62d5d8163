<?php
/**
 * Documentation System
 *
 * A more accessible documentation system for the Momentum platform
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once __DIR__ . '/../../src/utils/Session.php';

// Start session
Session::start();

// Define documentation categories and pages
$docCategories = [
    'ai-agent-army' => [
        'title' => 'AI Agent Army',
        'icon' => 'fa-robot',
        'color' => 'indigo',
        'pages' => [
            'comprehensive-plan' => [
                'title' => 'Comprehensive Implementation Plan',
                'file' => '../help/ai_agent_army_plan.md',
                'icon' => 'fa-sitemap',
                'description' => 'Complete strategy for building, deploying, and monetizing an AI Agent Army focused on rapid income generation.'
            ],
            'rapid-implementation' => [
                'title' => '24-Hour Rapid Implementation Plan',
                'file' => '../help/24_hour_implementation_plan.md',
                'icon' => 'fa-bolt',
                'description' => 'Steps to deploy a functional directory or affiliate website within 24 hours using existing web hosting and domain.'
            ],
            'brigade-structure' => [
                'title' => 'Brigade Structure & Organization',
                'file' => 'content/ai_agent_army/brigade_structure.md',
                'icon' => 'fa-layer-group',
                'description' => 'Detailed explanation of the brigade structure and organization within the AI Agent Army.'
            ]
        ]
    ],
    'agents' => [
        'title' => 'AI Agents',
        'icon' => 'fa-microchip',
        'color' => 'blue',
        'pages' => [
            'aegis-director' => [
                'title' => 'Aegis Director Agent',
                'file' => '../../aegis_director_README.md',
                'icon' => 'fa-shield-alt',
                'description' => 'Guide to using the Aegis Director agent for executive functioning support and project management.'
            ],
            'youtube-browser' => [
                'title' => 'YouTube Browser Agent',
                'file' => 'content/agents/youtube_browser.md',
                'icon' => 'fa-youtube',
                'description' => 'Documentation for the YouTube Browser agent that helps find and analyze YouTube content.'
            ]
        ]
    ],
    'getting-started' => [
        'title' => 'Getting Started',
        'icon' => 'fa-play-circle',
        'color' => 'green',
        'pages' => [
            'quick-start' => [
                'title' => 'Quick Start Guide',
                'file' => 'content/getting_started/quick_start.md',
                'icon' => 'fa-rocket',
                'description' => 'Get up and running with Momentum in minutes.'
            ],
            'system-overview' => [
                'title' => 'System Overview',
                'file' => 'content/getting_started/system_overview.md',
                'icon' => 'fa-sitemap',
                'description' => 'Overview of the Momentum system architecture and components.'
            ]
        ]
    ],
    'tutorials' => [
        'title' => 'Tutorials',
        'icon' => 'fa-graduation-cap',
        'color' => 'yellow',
        'pages' => [
            'create-agent' => [
                'title' => 'Creating a New Agent',
                'file' => 'content/tutorials/create_agent.md',
                'icon' => 'fa-plus-circle',
                'description' => 'Step-by-step guide to creating a new AI agent.'
            ],
            'agent-tasks' => [
                'title' => 'Working with Agent Tasks',
                'file' => 'content/tutorials/agent_tasks.md',
                'icon' => 'fa-tasks',
                'description' => 'Learn how to create, assign, and manage tasks for your AI agents.'
            ]
        ]
    ]
];

// Handle page request
$currentCategory = isset($_GET['category']) ? $_GET['category'] : null;
$currentPage = isset($_GET['page']) ? $_GET['page'] : null;

// Validate category and page
$validCategory = isset($docCategories[$currentCategory]);
$validPage = $validCategory && isset($docCategories[$currentCategory]['pages'][$currentPage]);

// Get page content if valid
$pageContent = '';
$pageTitle = 'Documentation';

if ($validCategory && $validPage) {
    $pageInfo = $docCategories[$currentCategory]['pages'][$currentPage];
    $pageTitle = $pageInfo['title'];
    $filePath = __DIR__ . '/' . $pageInfo['file'];

    if (file_exists($filePath)) {
        $pageContent = file_get_contents($filePath);
    } else {
        $pageContent = "# {$pageInfo['title']}\n\nThis documentation page is under construction.";
    }
}

// Function to convert Markdown to HTML
function markdownToHtml($markdown) {
    // Simple Markdown parser (for a production environment, use a proper Markdown library)
    $html = $markdown;

    // Headers
    $html = preg_replace('/^# (.*?)$/m', '<h1>$1</h1>', $html);
    $html = preg_replace('/^## (.*?)$/m', '<h2>$1</h2>', $html);
    $html = preg_replace('/^### (.*?)$/m', '<h3>$1</h3>', $html);
    $html = preg_replace('/^#### (.*?)$/m', '<h4>$1</h4>', $html);
    $html = preg_replace('/^##### (.*?)$/m', '<h5>$1</h5>', $html);

    // Bold and Italic
    $html = preg_replace('/\*\*(.*?)\*\*/s', '<strong>$1</strong>', $html);
    $html = preg_replace('/\*(.*?)\*/s', '<em>$1</em>', $html);

    // Lists
    $html = preg_replace('/^- (.*?)$/m', '<li>$1</li>', $html);
    $html = preg_replace('/^[0-9]+\. (.*?)$/m', '<li>$1</li>', $html);

    // Wrap lists in ul/ol tags
    $html = preg_replace('/<li>(.*?)<\/li>(?!\s*<li>)/s', '<ul><li>$1</li></ul>', $html);
    $html = preg_replace('/<\/ul>\s*<ul>/', '', $html);

    // Links
    $html = preg_replace('/\[(.*?)\]\((.*?)\)/s', '<a href="$2">$1</a>', $html);

    // Code blocks
    $html = preg_replace('/```(.*?)```/s', '<pre><code>$1</code></pre>', $html);
    $html = preg_replace('/`(.*?)`/s', '<code>$1</code>', $html);

    // Paragraphs
    $html = preg_replace('/^(?!<h|<ul|<li|<\/|<pre)(.*?)$/m', '<p>$1</p>', $html);

    return $html;
}

// Convert page content to HTML
if ($pageContent) {
    $pageContent = markdownToHtml($pageContent);
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?> - Momentum Documentation</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .sidebar {
            height: calc(100vh - 64px);
            overflow-y: auto;
        }
        .content {
            height: calc(100vh - 64px);
            overflow-y: auto;
        }
        .doc-content h1 {
            font-size: 2rem;
            font-weight: bold;
            margin-top: 1.5rem;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e5e7eb;
        }
        .doc-content h2 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }
        .doc-content h3 {
            font-size: 1.25rem;
            font-weight: bold;
            margin-top: 1.25rem;
            margin-bottom: 0.5rem;
        }
        .doc-content h4 {
            font-size: 1.1rem;
            font-weight: bold;
            margin-top: 1rem;
            margin-bottom: 0.5rem;
        }
        .doc-content p {
            margin-bottom: 1rem;
            line-height: 1.6;
        }
        .doc-content ul {
            list-style-type: disc;
            margin-left: 1.5rem;
            margin-bottom: 1rem;
        }
        .doc-content ol {
            list-style-type: decimal;
            margin-left: 1.5rem;
            margin-bottom: 1rem;
        }
        .doc-content li {
            margin-bottom: 0.25rem;
        }
        .doc-content a {
            color: #4f46e5;
            text-decoration: underline;
        }
        .doc-content a:hover {
            color: #4338ca;
        }
        .doc-content blockquote {
            border-left: 4px solid #e5e7eb;
            padding-left: 1rem;
            margin-left: 1rem;
            margin-bottom: 1rem;
            color: #6b7280;
        }
        .doc-content code {
            background-color: #f3f4f6;
            padding: 0.2rem 0.4rem;
            border-radius: 0.25rem;
            font-family: monospace;
        }
        .doc-content pre {
            background-color: #f3f4f6;
            padding: 1rem;
            border-radius: 0.25rem;
            overflow-x: auto;
            margin-bottom: 1rem;
        }
        .doc-content pre code {
            background-color: transparent;
            padding: 0;
        }
        .doc-content table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
        }
        .doc-content th, .doc-content td {
            border: 1px solid #e5e7eb;
            padding: 0.5rem;
        }
        .doc-content th {
            background-color: #f9fafb;
            font-weight: bold;
        }
        .category-indigo { border-color: #6366f1; }
        .category-blue { border-color: #3b82f6; }
        .category-green { border-color: #10b981; }
        .category-yellow { border-color: #f59e0b; }
        .category-red { border-color: #ef4444; }
        .category-purple { border-color: #8b5cf6; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <a href="/momentum/dashboard" class="flex items-center">
                    <i class="fas fa-book text-indigo-600 text-2xl mr-3"></i>
                    <h1 class="text-xl font-bold text-gray-900">Momentum Documentation</h1>
                </a>
            </div>
            <div class="flex items-center space-x-4">
                <a href="/momentum/ai-agents" class="text-gray-600 hover:text-indigo-600">
                    <i class="fas fa-robot mr-1"></i> AI Agents
                </a>
                <a href="/momentum/dashboard" class="text-gray-600 hover:text-indigo-600">
                    <i class="fas fa-tachometer-alt mr-1"></i> Dashboard
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="flex flex-col md:flex-row">
            <!-- Sidebar -->
            <div class="w-full md:w-64 flex-shrink-0 mb-6 md:mb-0 md:mr-6">
                <div class="bg-white rounded-lg shadow sidebar overflow-y-auto">
                    <div class="p-4 border-b border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900">Documentation</h2>
                    </div>
                    <nav class="p-4 space-y-6">
                        <?php foreach ($docCategories as $categoryId => $category): ?>
                            <div>
                                <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-2">
                                    <i class="fas <?= $category['icon'] ?> text-<?= $category['color'] ?>-500 mr-2"></i>
                                    <?= htmlspecialchars($category['title']) ?>
                                </h3>
                                <ul class="space-y-1">
                                    <?php foreach ($category['pages'] as $pageId => $page): ?>
                                        <li>
                                            <a href="?category=<?= $categoryId ?>&page=<?= $pageId ?>"
                                               class="block px-3 py-2 rounded-md text-sm font-medium <?= ($currentCategory === $categoryId && $currentPage === $pageId) ? 'bg-indigo-50 text-indigo-700 border-l-4 border-indigo-500' : 'text-gray-700 hover:bg-gray-50' ?>">
                                                <i class="fas <?= $page['icon'] ?> mr-2 <?= ($currentCategory === $categoryId && $currentPage === $pageId) ? 'text-indigo-500' : 'text-gray-400' ?>"></i>
                                                <?= htmlspecialchars($page['title']) ?>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endforeach; ?>
                    </nav>
                </div>
            </div>

            <!-- Content -->
            <div class="flex-1">
                <div class="bg-white rounded-lg shadow content overflow-y-auto">
                    <?php if ($validCategory && $validPage): ?>
                        <div class="p-6">
                            <div class="border-b border-gray-200 pb-4 mb-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 bg-<?= $docCategories[$currentCategory]['color'] ?>-100 p-2 rounded-md">
                                        <i class="fas <?= $docCategories[$currentCategory]['pages'][$currentPage]['icon'] ?> text-<?= $docCategories[$currentCategory]['color'] ?>-600"></i>
                                    </div>
                                    <div class="ml-4">
                                        <h2 class="text-2xl font-bold text-gray-900"><?= htmlspecialchars($docCategories[$currentCategory]['pages'][$currentPage]['title']) ?></h2>
                                        <p class="text-sm text-gray-500"><?= htmlspecialchars($docCategories[$currentCategory]['pages'][$currentPage]['description']) ?></p>
                                    </div>
                                </div>
                            </div>
                            <div class="doc-content">
                                <?= $pageContent ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to Momentum Documentation</h2>
                            <p class="text-gray-600 mb-6">Select a documentation page from the sidebar to get started.</p>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                                <?php foreach ($docCategories as $categoryId => $category): ?>
                                    <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition category-<?= $category['color'] ?> border-l-4">
                                        <h3 class="text-xl font-semibold text-gray-800 mb-2 flex items-center">
                                            <i class="fas <?= $category['icon'] ?> text-<?= $category['color'] ?>-500 mr-2"></i>
                                            <?= htmlspecialchars($category['title']) ?>
                                        </h3>
                                        <ul class="mt-4 space-y-2">
                                            <?php foreach ($category['pages'] as $pageId => $page): ?>
                                                <li>
                                                    <a href="?category=<?= $categoryId ?>&page=<?= $pageId ?>" class="text-indigo-600 hover:text-indigo-800 flex items-start">
                                                        <i class="fas <?= $page['icon'] ?> text-gray-400 mr-2 mt-1"></i>
                                                        <span><?= htmlspecialchars($page['title']) ?></span>
                                                    </a>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Highlight current page in sidebar
            const currentCategory = '<?= $currentCategory ?>';
            const currentPage = '<?= $currentPage ?>';

            if (currentCategory && currentPage) {
                const activeLink = document.querySelector(`a[href="?category=${currentCategory}&page=${currentPage}"]`);
                if (activeLink) {
                    activeLink.scrollIntoView({ block: 'center' });
                }
            }
        });
    </script>
</body>
</html>