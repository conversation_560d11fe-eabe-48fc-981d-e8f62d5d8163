<?php
/**
 * Note Controller
 *
 * Handles note-related functionality for the information hub.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Note.php';

class NoteController extends BaseController {
    private $noteModel;

    public function __construct() {
        $this->noteModel = new Note();
    }

    /**
     * Show note list
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Get notes based on filters
        $notes = $this->noteModel->getUserNotes($userId, $filters);

        // Get note categories
        $categories = $this->noteModel->getUserNoteCategories($userId);

        $this->view('notes/index', [
            'notes' => $notes,
            'categories' => $categories,
            'filters' => $filters
        ]);
    }

    /**
     * Show note creation form
     */
    public function create() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get note categories for dropdown
        $categories = $this->noteModel->getUserNoteCategories($userId);

        $this->view('notes/create', [
            'categories' => $categories
        ]);
    }

    /**
     * Process note creation
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['title']);

        if (!empty($errors)) {
            // Get note categories for dropdown
            $categories = $this->noteModel->getUserNoteCategories($userId);

            $this->view('notes/create', [
                'errors' => $errors,
                'data' => $data,
                'categories' => $categories
            ]);
            return;
        }

        // Prepare note data
        $noteData = [
            'user_id' => $userId,
            'title' => $data['title'],
            'content' => $data['content'] ?? null,
            'category' => $data['category'] ?? null,
            'tags' => $data['tags'] ?? null,
            'is_pinned' => isset($data['is_pinned']) ? 1 : 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create note
        $noteId = $this->noteModel->create($noteData);

        if ($noteId) {
            Session::setFlash('success', 'Note created successfully');
            $this->redirect('/notes');
        } else {
            Session::setFlash('error', 'Failed to create note');

            // Get note categories for dropdown
            $categories = $this->noteModel->getUserNoteCategories($userId);

            $this->view('notes/create', [
                'data' => $data,
                'categories' => $categories
            ]);
        }
    }

    /**
     * Show note details
     */
    public function viewNote($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get note
        $note = $this->noteModel->find($id);

        // Verify note exists and belongs to user
        if (!$note || $note['user_id'] != $userId) {
            Session::setFlash('error', 'Note not found');
            $this->redirect('/notes');
        }

        $this->view('notes/view', [
            'note' => $note
        ]);
    }

    /**
     * Show note edit form
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get note
        $note = $this->noteModel->find($id);

        // Verify note exists and belongs to user
        if (!$note || $note['user_id'] != $userId) {
            Session::setFlash('error', 'Note not found');
            $this->redirect('/notes');
        }

        // Get note categories for dropdown
        $categories = $this->noteModel->getUserNoteCategories($userId);

        $this->view('notes/edit', [
            'note' => $note,
            'categories' => $categories
        ]);
    }

    /**
     * Process note update
     */
    public function update($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get note
        $note = $this->noteModel->find($id);

        // Verify note exists and belongs to user
        if (!$note || $note['user_id'] != $userId) {
            Session::setFlash('error', 'Note not found');
            $this->redirect('/notes');
        }

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['title']);

        if (!empty($errors)) {
            // Get note categories for dropdown
            $categories = $this->noteModel->getUserNoteCategories($userId);

            $this->view('notes/edit', [
                'errors' => $errors,
                'note' => array_merge($note, $data),
                'categories' => $categories
            ]);
            return;
        }

        // Prepare note data
        $noteData = [
            'title' => $data['title'],
            'content' => $data['content'] ?? null,
            'category' => $data['category'] ?? null,
            'tags' => $data['tags'] ?? null,
            'is_pinned' => isset($data['is_pinned']) ? 1 : 0,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update note
        $result = $this->noteModel->update($id, $noteData);

        if ($result) {
            Session::setFlash('success', 'Note updated successfully');
            $this->redirect('/notes/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update note');

            // Get note categories for dropdown
            $categories = $this->noteModel->getUserNoteCategories($userId);

            $this->view('notes/edit', [
                'note' => array_merge($note, $data),
                'categories' => $categories
            ]);
        }
    }

    /**
     * Delete note
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get note
        $note = $this->noteModel->find($id);

        // Verify note exists and belongs to user
        if (!$note || $note['user_id'] != $userId) {
            Session::setFlash('error', 'Note not found');
            $this->redirect('/notes');
        }

        // Delete note
        $result = $this->noteModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Note deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete note');
        }

        $this->redirect('/notes');
    }

    /**
     * Search notes
     */
    public function search() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $searchTerm = $this->getQueryData()['q'] ?? '';

        if (empty($searchTerm)) {
            $this->redirect('/notes');
        }

        // Search notes
        $notes = $this->noteModel->searchNotes($userId, $searchTerm);

        // Get note categories
        $categories = $this->noteModel->getUserNoteCategories($userId);

        $this->view('notes/index', [
            'notes' => $notes,
            'categories' => $categories,
            'searchTerm' => $searchTerm
        ]);
    }

    /**
     * Toggle pin status
     */
    public function togglePin($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get note
        $note = $this->noteModel->find($id);

        // Verify note exists and belongs to user
        if (!$note || $note['user_id'] != $userId) {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Note not found'], 404);
            } else {
                Session::setFlash('error', 'Note not found');
                $this->redirect('/notes');
            }
            return;
        }

        // Toggle pin status
        $isPinned = $note['is_pinned'] ? 0 : 1;

        $result = $this->noteModel->update($id, [
            'is_pinned' => $isPinned,
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        if ($result) {
            if ($this->isAjax()) {
                $this->json(['success' => true, 'is_pinned' => $isPinned]);
            } else {
                Session::setFlash('success', $isPinned ? 'Note pinned successfully' : 'Note unpinned successfully');
                $this->redirect('/notes');
            }
        } else {
            if ($this->isAjax()) {
                $this->json(['success' => false, 'message' => 'Failed to update pin status'], 500);
            } else {
                Session::setFlash('error', 'Failed to update pin status');
                $this->redirect('/notes');
            }
        }
    }
}
