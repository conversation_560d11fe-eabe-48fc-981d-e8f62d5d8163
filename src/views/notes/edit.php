
<!-- Force browser refresh with timestamp -->
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">

<style>
/* Enhanced Notes Edit Page Styles - Force Override - <?= time() ?> */
.tools-container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 2rem !important;
    min-height: 100vh !important;
    background: #f8fafc !important;
}

.gallery-header {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    margin-bottom: 2rem !important;
    padding: 1.5rem !important;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
    border-radius: 1rem !important;
    border: 1px solid #e2e8f0 !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.gallery-title {
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: #1e293b !important;
    margin-bottom: 0.5rem !important;
}

.gallery-subtitle {
    color: #64748b !important;
    font-size: 1rem !important;
}

.shadow-soft {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Enhanced gradient sections */
.bg-gradient-to-br {
    border-radius: 1rem !important;
    padding: 1.5rem !important;
    margin-bottom: 1.5rem !important;
    border: 1px solid !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.from-blue-50.to-indigo-50 {
    background: linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%) !important;
    border-color: #93c5fd !important;
}

.from-green-50.to-emerald-50 {
    background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%) !important;
    border-color: #86efac !important;
}

.from-purple-50.to-pink-50 {
    background: linear-gradient(135deg, #faf5ff 0%, #fdf2f8 100%) !important;
    border-color: #c084fc !important;
}

.from-orange-50.to-yellow-50 {
    background: linear-gradient(135deg, #fff7ed 0%, #fefce8 100%) !important;
    border-color: #fdba74 !important;
}

/* Enhanced form elements */
input[type="text"], textarea, select {
    padding: 0.75rem 1rem !important;
    border-radius: 0.5rem !important;
    border: 2px solid #d1d5db !important;
    font-size: 1rem !important;
    transition: all 0.2s ease !important;
}

input[type="text"]:focus, textarea:focus, select:focus {
    outline: none !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Enhanced buttons */
.bg-blue-600 {
    background-color: #2563eb !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 0.5rem !important;
    font-weight: 600 !important;
    transition: all 0.2s ease !important;
}

.bg-blue-600:hover {
    background-color: #1d4ed8 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3) !important;
}

.bg-gray-600 {
    background-color: #4b5563 !important;
    padding: 0.75rem 1.5rem !important;
    border-radius: 0.5rem !important;
    font-weight: 600 !important;
    transition: all 0.2s ease !important;
}

.bg-gray-600:hover {
    background-color: #374151 !important;
    transform: translateY(-1px) !important;
}

/* Enhanced icons */
.fas {
    margin-right: 0.5rem !important;
}

.text-blue-500 {
    color: #3b82f6 !important;
}

.text-green-500 {
    color: #10b981 !important;
}

.text-purple-500 {
    color: #8b5cf6 !important;
}

.text-orange-500 {
    color: #f97316 !important;
}

.text-yellow-500 {
    color: #eab308 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .tools-container {
        padding: 1rem !important;
    }

    .gallery-header {
        flex-direction: column !important;
        gap: 1rem !important;
        text-align: center !important;
    }

    .grid.md\\:grid-cols-2 {
        grid-template-columns: 1fr !important;
    }
}
</style>

<div class="tools-container">
    <!-- Header -->
    <div class="gallery-header">
        <div class="flex items-center">
            <a href="/momentum/notes/view/<?= $note['id'] ?>" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 mr-4 text-xl">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div>
                <h1 class="gallery-title">Edit Note</h1>
                <p class="gallery-subtitle">Update your note content and settings</p>
            </div>
        </div>
        <div class="flex space-x-3">
            <a href="/momentum/notes/view/<?= $note['id'] ?>" class="inline-flex items-center px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-eye mr-2"></i>
                View Note
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-soft border border-gray-200 dark:border-gray-700 overflow-hidden mb-8">
        <div class="px-6 py-8">
            <form action="/momentum/notes/edit/<?= $note['id'] ?>" method="POST" class="space-y-8">
                <!-- Display validation errors if any -->
                <?php if (isset($errors) && !empty($errors)): ?>
                    <div class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 p-6 rounded-xl">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-red-500 dark:text-red-400 text-lg"></i>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-red-800 dark:text-red-200 mb-3">
                                    <i class="fas fa-exclamation-circle mr-2"></i>
                                    Please fix the following errors:
                                </h3>
                                <div class="text-red-700 dark:text-red-300">
                                    <ul class="list-disc pl-5 space-y-2">
                                        <?php foreach ($errors as $error): ?>
                                            <li class="text-sm"><?= $error ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Title Section -->
                <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-xl border border-blue-200 dark:border-blue-800">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                        <i class="fas fa-heading mr-3 text-blue-500"></i>
                        Note Title
                    </h3>
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Title <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="title" id="title"
                               class="w-full rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-lg dark:bg-gray-800 dark:text-white transition-all duration-200"
                               value="<?= View::escape($note['title']) ?>"
                               required
                               placeholder="Enter a descriptive title for your note...">
                    </div>
                </div>

                <!-- Content Section -->
                <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-xl border border-green-200 dark:border-green-800">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                        <i class="fas fa-file-alt mr-3 text-green-500"></i>
                        Note Content
                    </h3>
                    <div>
                        <label for="content" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Content
                        </label>
                        <textarea name="content" id="content" rows="12"
                                  class="w-full rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-green-500 focus:ring-green-500 dark:bg-gray-800 dark:text-white transition-all duration-200 resize-y"
                                  placeholder="Write your note content here..."><?= View::escape($note['content']) ?></textarea>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                            <i class="fas fa-info-circle mr-1"></i>
                            You can use line breaks and formatting in your content.
                        </p>
                    </div>
                </div>

                <!-- Organization Section -->
                <div class="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-xl border border-purple-200 dark:border-purple-800">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                        <i class="fas fa-tags mr-3 text-purple-500"></i>
                        Organization & Tags
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Category -->
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                <i class="fas fa-folder mr-2 text-purple-500"></i>
                                Category
                            </label>
                            <input type="text" name="category" id="category" list="category-list"
                                   class="w-full rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-purple-500 focus:ring-purple-500 dark:bg-gray-800 dark:text-white transition-all duration-200"
                                   value="<?= View::escape($note['category']) ?>"
                                   placeholder="e.g., Personal, Work, Ideas...">
                            <datalist id="category-list">
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= View::escape($category) ?>">
                                <?php endforeach; ?>
                            </datalist>
                        </div>

                        <!-- Tags -->
                        <div>
                            <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                <i class="fas fa-hashtag mr-2 text-purple-500"></i>
                                Tags
                            </label>
                            <input type="text" name="tags" id="tags"
                                   class="w-full rounded-lg border-gray-300 dark:border-gray-600 shadow-sm focus:border-purple-500 focus:ring-purple-500 dark:bg-gray-800 dark:text-white transition-all duration-200"
                                   value="<?= View::escape($note['tags']) ?>"
                                   placeholder="work, project, important">
                        </div>
                    </div>

                    <p class="mt-4 text-sm text-gray-500 dark:text-gray-400 bg-white/50 dark:bg-gray-800/50 p-3 rounded-lg">
                        <i class="fas fa-lightbulb mr-2 text-yellow-500"></i>
                        <strong>Tip:</strong> Use categories to group similar notes and tags for easy searching. Separate multiple tags with commas.
                    </p>
                </div>

                <!-- Settings Section -->
                <div class="bg-gradient-to-br from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 p-6 rounded-xl border border-orange-200 dark:border-orange-800">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                        <i class="fas fa-cog mr-3 text-orange-500"></i>
                        Note Settings
                    </h3>

                    <div class="flex items-center p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg border border-orange-200 dark:border-orange-700">
                        <input type="checkbox" name="is_pinned" id="is_pinned"
                               class="h-5 w-5 text-orange-600 focus:ring-orange-500 border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 transition-all duration-200"
                               value="1" <?= $note['is_pinned'] ? 'checked' : '' ?>>
                        <label for="is_pinned" class="ml-3 flex items-center text-gray-700 dark:text-gray-300">
                            <i class="fas fa-thumbtack mr-2 text-orange-500"></i>
                            <span class="font-medium">Pin this note</span>
                        </label>
                    </div>
                    <p class="mt-3 text-sm text-gray-500 dark:text-gray-400">
                        <i class="fas fa-info-circle mr-1"></i>
                        Pinned notes appear at the top of your notes list for quick access.
                    </p>
                </div>

                <!-- Form Actions -->
                <div class="flex flex-col sm:flex-row justify-between items-center gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <i class="fas fa-clock mr-2"></i>
                        Last updated: <?= date('M j, Y \a\t g:i A', strtotime($note['updated_at'])) ?>
                    </div>

                    <div class="flex space-x-3">
                        <a href="/momentum/notes/view/<?= $note['id'] ?>"
                           class="inline-flex items-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm text-sm font-semibold text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 transform hover:-translate-y-1">
                            <i class="fas fa-times mr-2"></i>
                            Cancel
                        </a>
                        <button type="submit"
                                class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:-translate-y-1 shadow-lg hover:shadow-xl">
                            <i class="fas fa-save mr-2"></i>
                            Update Note
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
